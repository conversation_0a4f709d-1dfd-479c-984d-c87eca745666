import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY || 'demo-key-for-testing',
  dangerouslyAllowBrowser: true
})

// Simulated web search function (in production, use real search API like <PERSON><PERSON>, <PERSON><PERSON>, etc.)
const performWebSearch = async (query) => {
  try {
    // For demo purposes, we'll simulate search results
    // In production, integrate with actual search APIs
    const searchResults = [
      {
        title: `Latest information about: ${query}`,
        url: `https://example.com/search?q=${encodeURIComponent(query)}`,
        snippet: `This is simulated search result content for "${query}". In a real implementation, this would contain actual web search results from APIs like <PERSON><PERSON>, <PERSON><PERSON>, or DuckDuckGo.`,
        timestamp: new Date().toISOString()
      },
      {
        title: `${query} - Recent Updates`,
        url: `https://example.com/updates/${encodeURIComponent(query)}`,
        snippet: `Recent developments and updates related to ${query}. This demonstrates how the agent can gather current information from the web.`,
        timestamp: new Date().toISOString()
      }
    ]

    return searchResults
  } catch (error) {
    console.error('Web search error:', error)
    return []
  }
}

// Enhanced agent that can use tools
export const processAgentRequest = async (message, conversationHistory = []) => {
  try {
    // Detect if this is a search request
    const searchKeywords = ['search', 'find', 'look up', 'latest', 'current', 'news', 'information about']
    const isSearchRequest = searchKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    )

    let searchResults = []
    let searchQuery = ''

    if (isSearchRequest) {
      // Extract search query from the message
      searchQuery = extractSearchQuery(message)
      searchResults = await performWebSearch(searchQuery)
    }

    // Prepare context for the AI
    let systemPrompt = `You are an AI assistant integrated with a collaborative text editor. You have access to web search capabilities and can help users with:

1. Normal conversations and questions
2. Editing and improving document content
3. Web searches for current information
4. Inserting search results and summaries into the editor

When users ask you to search for information or need current data, you can access web search results.
When users ask you to insert content into the editor, provide the content they need.

Current conversation context: The user is working in a collaborative editor.`

    if (searchResults.length > 0) {
      systemPrompt += `\n\nWeb search results for "${searchQuery}":\n${searchResults.map((result, index) => 
        `${index + 1}. ${result.title}\n   ${result.snippet}\n   URL: ${result.url}\n`
      ).join('\n')}`
    }

    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-8).map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      { role: 'user', content: message }
    ]

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages,
      max_tokens: 1000,
      temperature: 0.7
    })

    const content = response.choices[0].message.content.trim()

    // Determine if this should modify the editor
    const editorKeywords = ['insert', 'add', 'write', 'create', 'generate', 'summary', 'summarize']
    const shouldInsertToEditor = editorKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    ) || (isSearchRequest && message.toLowerCase().includes('insert'))

    let action = null
    let editorContent = null

    if (shouldInsertToEditor && searchResults.length > 0) {
      // Create a summary of search results for insertion
      action = 'insert'
      editorContent = `\n\n## Search Results: ${searchQuery}\n\n${searchResults.map((result, index) => 
        `### ${index + 1}. ${result.title}\n${result.snippet}\n\n`
      ).join('')}*Search performed on ${new Date().toLocaleDateString()}*\n\n`
    } else if (shouldInsertToEditor) {
      action = 'insert'
      // Extract content that should be inserted
      const lines = content.split('\n')
      const contentLines = lines.filter(line => 
        !line.toLowerCase().includes('i\'ll') && 
        !line.toLowerCase().includes('here\'s') &&
        !line.toLowerCase().includes('i\'ve') &&
        line.trim().length > 0
      )
      
      if (contentLines.length > 0) {
        editorContent = contentLines.join('\n')
      }
    }

    return {
      content,
      action,
      editorContent,
      searchResults: searchResults.length > 0 ? searchResults : null,
      searchQuery: searchQuery || null
    }
  } catch (error) {
    console.error('Agent processing error:', error)
    
    // Fallback response
    return {
      content: "I'm having trouble processing your request right now. Please try again or rephrase your question.",
      action: null,
      editorContent: null,
      searchResults: null,
      searchQuery: null
    }
  }
}

// Extract search query from user message
const extractSearchQuery = (message) => {
  // Simple extraction - in production, use more sophisticated NLP
  const searchPatterns = [
    /search for (.+)/i,
    /find (.+)/i,
    /look up (.+)/i,
    /information about (.+)/i,
    /latest (.+)/i,
    /current (.+)/i,
    /news about (.+)/i
  ]

  for (const pattern of searchPatterns) {
    const match = message.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  // Fallback: use the entire message as search query
  return message.replace(/please|can you|could you|search|find|look up/gi, '').trim()
}

// Tool definitions for the agent
export const availableTools = [
  {
    name: 'web_search',
    description: 'Search the web for current information',
    parameters: {
      query: 'Search query string'
    }
  },
  {
    name: 'insert_content',
    description: 'Insert content into the editor',
    parameters: {
      content: 'Content to insert'
    }
  },
  {
    name: 'edit_text',
    description: 'Edit selected text in the editor',
    parameters: {
      original: 'Original text',
      edited: 'Edited text'
    }
  }
]

